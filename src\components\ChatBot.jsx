import { useState } from "react";
import axios from "axios";

function ChatBot() {
  const [question, setQuestion] = useState("");
  const [chat, setChat] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleSend = async () => {
    if (!question.trim()) return;

    const userMessage = { from: "user", text: question };
    setChat((prev) => [...prev, userMessage]);
    setLoading(true);

    try {
      const res = await axios.post("/api/faq/ask", { question });
      const botMessage = { from: "bot", text: res.data.answer };
      setChat((prev) => [...prev, botMessage]);
    } catch (error) {
      const errorMessage = {
        from: "bot",
        text: "حدث خطأ أثناء الاتصال بالخادم.",
      };
      setChat((prev) => [...prev, errorMessage]);
    }

    setQuestion("");
    setLoading(false);
  };

  return (
    <div className="container mt-5" style={{ maxWidth: "600px" }}>
      <h4 className="mb-4 text-center">🤖 مهندس معماري - شات بوت</h4>

      <div
        className="border rounded p-3 mb-3"
        style={{ minHeight: "300px", background: "#f9f9f9" }}
      >
        {chat.map((msg, index) => (
          <div
            key={index}
            className={`mb-2 text-${msg.from === "user" ? "end" : "start"}`}
          >
            <span
              className={`d-inline-block p-2 rounded ${
                msg.from === "user"
                  ? "bg-primary text-white"
                  : "bg-light text-dark"
              }`}
              style={{ maxWidth: "80%" }}
            >
              {msg.text}
            </span>
          </div>
        ))}

        {loading && (
          <div className="text-start text-muted">
            <span className="spinner-border spinner-border-sm me-2"></span>جاري
            الرد...
          </div>
        )}
      </div>

      <div className="input-group">
        <input
          type="text"
          className="form-control"
          placeholder="اكتب سؤالك..."
          value={question}
          onChange={(e) => setQuestion(e.target.value)}
          onKeyDown={(e) => e.key === "Enter" && handleSend()}
        />
        <button className="btn btn-primary" onClick={handleSend}>
          إرسال
        </button>
      </div>
    </div>
  );
}

export default ChatBot;
