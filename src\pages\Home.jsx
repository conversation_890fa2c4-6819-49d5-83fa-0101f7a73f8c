import { useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import "../styles/animations.css";

function Home() {
  const navigate = useNavigate();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <>
      {/* Hero Section */}
      <section className="hero-section position-relative overflow-hidden">
        <div
          className="animate-gradient position-absolute w-100 h-100"
          style={{ zIndex: -1 }}
        ></div>
        <div className="container text-center py-5">
          <div className={`${isVisible ? "animate-fade-in-up" : ""}`}>
            <h1 className="display-3 fw-bold text-white mb-4">
              🏗️ المهندس المعماري
            </h1>
            <p className="lead text-white-50 mb-5 fs-4">
              تصميمات معمارية أنيقة ووظيفية مصممة بإبداع ودقة
            </p>
          </div>

          <div className={`${isVisible ? "animate-scale-in stagger-2" : ""}`}>
            <button
              className="btn btn-light btn-lg px-5 py-3 me-3 hover-lift"
              onClick={() => navigate("/chat")}
            >
              💬 تحدث مع ArchiBot
            </button>
            <button
              className="btn btn-outline-light btn-lg px-5 py-3 hover-lift"
              onClick={() => navigate("/portfolio")}
            >
              🎨 شاهد أعمالي
            </button>
          </div>
        </div>

        {/* Floating Buildings */}
        <div
          className="position-absolute"
          style={{ top: "20%", left: "10%", zIndex: 1 }}
        >
          <div className="animate-float stagger-1">
            <i
              className="fas fa-building text-white-50"
              style={{ fontSize: "3rem" }}
            ></i>
          </div>
        </div>
        <div
          className="position-absolute"
          style={{ top: "60%", right: "15%", zIndex: 1 }}
        >
          <div className="animate-float stagger-3">
            <i
              className="fas fa-city text-white-50"
              style={{ fontSize: "2.5rem" }}
            ></i>
          </div>
        </div>
        <div
          className="position-absolute"
          style={{ top: "40%", right: "5%", zIndex: 1 }}
        >
          <div className="animate-float stagger-5">
            <i
              className="fas fa-home text-white-50"
              style={{ fontSize: "2rem" }}
            ></i>
          </div>
        </div>
      </section>

      {/* Services Preview */}
      <section className="py-5 bg-light">
        <div className="container">
          <div className="row text-center">
            <div className="col-md-4 mb-4">
              <div
                className={`card h-100 border-0 shadow-sm hover-lift ${
                  isVisible ? "animate-fade-in-up stagger-2" : ""
                }`}
              >
                <div className="card-body p-4">
                  <div className="building-icon mb-3">
                    <i
                      className="fas fa-drafting-compass text-primary"
                      style={{ fontSize: "3rem" }}
                    ></i>
                  </div>
                  <h5 className="card-title">التصميم المعماري</h5>
                  <p className="card-text text-muted">
                    تصميمات معمارية مبتكرة للمشاريع السكنية والتجارية
                  </p>
                </div>
              </div>
            </div>

            <div className="col-md-4 mb-4">
              <div
                className={`card h-100 border-0 shadow-sm hover-lift ${
                  isVisible ? "animate-fade-in-up stagger-3" : ""
                }`}
              >
                <div className="card-body p-4">
                  <div className="building-icon mb-3">
                    <i
                      className="fas fa-cube text-primary"
                      style={{ fontSize: "3rem" }}
                    ></i>
                  </div>
                  <h5 className="card-title">التصميم ثلاثي الأبعاد</h5>
                  <p className="card-text text-muted">
                    نماذج ثلاثية الأبعاد عالية الجودة لتصور المشروع
                  </p>
                </div>
              </div>
            </div>

            <div className="col-md-4 mb-4">
              <div
                className={`card h-100 border-0 shadow-sm hover-lift ${
                  isVisible ? "animate-fade-in-up stagger-4" : ""
                }`}
              >
                <div className="card-body p-4">
                  <div className="building-icon mb-3">
                    <i
                      className="fas fa-hard-hat text-primary"
                      style={{ fontSize: "3rem" }}
                    ></i>
                  </div>
                  <h5 className="card-title">الإشراف على التنفيذ</h5>
                  <p className="card-text text-muted">
                    متابعة دقيقة لضمان تطبيق التصميم بأعلى جودة
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}

export default Home;
