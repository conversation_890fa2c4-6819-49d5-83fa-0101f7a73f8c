import { useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import "../styles/animations.css";

function Home() {
  const navigate = useNavigate();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <>
      {/* Hero Section */}
      <section className="hero-section position-relative overflow-hidden min-vh-100 d-flex align-items-center">
        <div
          className="position-absolute w-100 h-100"
          style={{
            background:
              "linear-gradient(-45deg, #6c757d, #495057, #343a40, #212529)",
            backgroundSize: "400% 400%",
            animation: "grayGradientShift 15s ease infinite",
            zIndex: -2,
          }}
        ></div>

        {/* Animated Background Elements */}
        <div
          className="position-absolute w-100 h-100 overflow-hidden"
          style={{ zIndex: -1 }}
        >
          {/* Large Architecture Icons */}
          <div
            className="position-absolute animate-float-slow"
            style={{ top: "10%", left: "5%" }}
          >
            <i
              className="fas fa-building"
              style={{
                fontSize: "6rem",
                color: "rgba(255,255,255,0.1)",
                filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.2))",
              }}
            ></i>
          </div>

          <div
            className="position-absolute animate-float-medium"
            style={{ top: "20%", right: "8%" }}
          >
            <i
              className="fas fa-city"
              style={{
                fontSize: "5rem",
                color: "rgba(255,255,255,0.08)",
                filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.2))",
              }}
            ></i>
          </div>

          <div
            className="position-absolute animate-float-fast"
            style={{ bottom: "15%", left: "10%" }}
          >
            <i
              className="fas fa-home"
              style={{
                fontSize: "4rem",
                color: "rgba(255,255,255,0.12)",
                filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.2))",
              }}
            ></i>
          </div>

          <div
            className="position-absolute animate-float-slow"
            style={{ bottom: "25%", right: "15%" }}
          >
            <i
              className="fas fa-drafting-compass"
              style={{
                fontSize: "3.5rem",
                color: "rgba(255,255,255,0.09)",
                filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.2))",
              }}
            ></i>
          </div>

          {/* Geometric Shapes */}
          <div
            className="position-absolute animate-rotate-slow"
            style={{ top: "30%", left: "20%" }}
          >
            <div
              style={{
                width: "100px",
                height: "100px",
                background:
                  "linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))",
                transform: "rotate(45deg)",
                borderRadius: "20px",
                border: "2px solid rgba(255,255,255,0.2)",
              }}
            ></div>
          </div>

          <div
            className="position-absolute animate-float-medium"
            style={{ top: "60%", right: "25%" }}
          >
            <div
              style={{
                width: "80px",
                height: "80px",
                background:
                  "linear-gradient(135deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03))",
                borderRadius: "50%",
                border: "1px solid rgba(255,255,255,0.15)",
              }}
            ></div>
          </div>

          {/* Floating Particles */}
          {[...Array(12)].map((_, i) => (
            <div
              key={i}
              className="position-absolute animate-particle"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 15}s`,
                animationDuration: `${15 + Math.random() * 10}s`,
              }}
            >
              <div
                style={{
                  width: "6px",
                  height: "6px",
                  background: "rgba(255,255,255,0.7)",
                  borderRadius: "50%",
                  boxShadow: "0 0 15px rgba(255,255,255,0.9)",
                }}
              ></div>
            </div>
          ))}
        </div>

        <div className="container text-center position-relative">
          <div className={`${isVisible ? "animate-fade-in-up" : ""}`}>
            <h1
              className="display-2 fw-bold text-white mb-4"
              style={{
                textShadow: "3px 3px 6px rgba(0,0,0,0.5)",
                background: "linear-gradient(45deg, #fff, #f8f9fa)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                backgroundClip: "text",
              }}
            >
              🏗️ المهندس المعماري
            </h1>
            <p
              className="lead text-white mb-5 fs-3"
              style={{
                textShadow: "2px 2px 4px rgba(0,0,0,0.4)",
                maxWidth: "800px",
                margin: "0 auto",
              }}
            >
              تصميمات معمارية أنيقة ووظيفية مصممة بإبداع ودقة لتحويل أحلامك إلى
              واقع
            </p>
          </div>

          <div
            className={`${
              isVisible ? "animate-scale-in stagger-2" : ""
            } d-flex flex-wrap justify-content-center gap-3`}
          >
            <button
              className="btn btn-light btn-lg px-5 py-3 hover-lift glow-effect"
              onClick={() => navigate("/chat")}
              style={{
                borderRadius: "25px",
                background: "rgba(255,255,255,0.95)",
                backdropFilter: "blur(10px)",
                border: "2px solid rgba(255,255,255,0.3)",
                fontWeight: "600",
                fontSize: "1.1rem",
              }}
            >
              <i className="fas fa-robot me-2"></i>
              💬 تحدث مع ArchiBot
            </button>
            <button
              className="btn btn-outline-light btn-lg px-5 py-3 hover-lift"
              onClick={() => navigate("/portfolio")}
              style={{
                borderRadius: "25px",
                border: "2px solid rgba(255,255,255,0.6)",
                background: "rgba(255,255,255,0.1)",
                backdropFilter: "blur(10px)",
                fontWeight: "600",
                fontSize: "1.1rem",
              }}
            >
              <i className="fas fa-images me-2"></i>
              🎨 شاهد أعمالي
            </button>
            <button
              className="btn btn-outline-light btn-lg px-5 py-3 hover-lift"
              onClick={() => navigate("/services")}
              style={{
                borderRadius: "25px",
                border: "2px solid rgba(255,255,255,0.6)",
                background: "rgba(255,255,255,0.1)",
                backdropFilter: "blur(10px)",
                fontWeight: "600",
                fontSize: "1.1rem",
              }}
            >
              <i className="fas fa-tools me-2"></i>
              🛠️ خدماتنا
            </button>
          </div>
        </div>
      </section>

      {/* Services Preview */}
      <section
        className="py-5 position-relative overflow-hidden"
        style={{
          background: "linear-gradient(135deg, #6c757d 0%, #495057 100%)",
        }}
      >
        {/* Background Pattern */}
        <div className="position-absolute w-100 h-100" style={{ opacity: 0.1 }}>
          <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <pattern
                id="grid-pattern"
                width="50"
                height="50"
                patternUnits="userSpaceOnUse"
              >
                <path
                  d="M 50 0 L 0 0 0 50"
                  fill="none"
                  stroke="white"
                  strokeWidth="1"
                />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid-pattern)" />
          </svg>
        </div>

        <div className="container position-relative">
          <div className="text-center mb-5">
            <h2
              className="display-5 fw-bold text-white mb-3"
              style={{ textShadow: "2px 2px 4px rgba(0,0,0,0.3)" }}
            >
              🛠️ خدماتنا المميزة
            </h2>
            <p
              className="lead text-white-50"
              style={{ textShadow: "1px 1px 2px rgba(0,0,0,0.3)" }}
            >
              نقدم حلول معمارية شاملة ومتطورة
            </p>
          </div>

          <div className="row text-center g-4">
            <div className="col-lg-4 col-md-6 mb-4">
              <div
                className={`card h-100 border-0 shadow-lg hover-lift card-3d ${
                  isVisible ? "animate-fade-in-up stagger-2" : ""
                }`}
                style={{
                  background: "rgba(255, 255, 255, 0.95)",
                  backdropFilter: "blur(10px)",
                  borderRadius: "20px",
                  transition: "all 0.3s ease",
                }}
              >
                <div className="card-body p-5">
                  <div className="building-icon mb-4 position-relative">
                    <i
                      className="fas fa-drafting-compass"
                      style={{
                        fontSize: "4rem",
                        background: "linear-gradient(45deg, #6c757d, #495057)",
                        WebkitBackgroundClip: "text",
                        WebkitTextFillColor: "transparent",
                        backgroundClip: "text",
                        filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.2))",
                      }}
                    ></i>
                  </div>
                  <h5
                    className="card-title fw-bold mb-3"
                    style={{ color: "#333" }}
                  >
                    التصميم المعماري
                  </h5>
                  <p className="card-text text-muted lh-lg">
                    تصميمات معمارية مبتكرة للمشاريع السكنية والتجارية مع مراعاة
                    أحدث المعايير
                  </p>
                  <div
                    className="mt-3"
                    style={{
                      width: "50px",
                      height: "3px",
                      background: "linear-gradient(45deg, #6c757d, #495057)",
                      borderRadius: "2px",
                      margin: "0 auto",
                    }}
                  ></div>
                </div>
              </div>
            </div>

            <div className="col-lg-4 col-md-6 mb-4">
              <div
                className={`card h-100 border-0 shadow-lg hover-lift card-3d ${
                  isVisible ? "animate-fade-in-up stagger-3" : ""
                }`}
                style={{
                  background: "rgba(255, 255, 255, 0.95)",
                  backdropFilter: "blur(10px)",
                  borderRadius: "20px",
                  transition: "all 0.3s ease",
                }}
              >
                <div className="card-body p-5">
                  <div className="building-icon mb-4">
                    <i
                      className="fas fa-cube"
                      style={{
                        fontSize: "4rem",
                        background: "linear-gradient(45deg, #495057, #343a40)",
                        WebkitBackgroundClip: "text",
                        WebkitTextFillColor: "transparent",
                        backgroundClip: "text",
                        filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.2))",
                      }}
                    ></i>
                  </div>
                  <h5
                    className="card-title fw-bold mb-3"
                    style={{ color: "#333" }}
                  >
                    التصميم ثلاثي الأبعاد
                  </h5>
                  <p className="card-text text-muted lh-lg">
                    نماذج ثلاثية الأبعاد عالية الجودة لتصور المشروع قبل التنفيذ
                  </p>
                  <div
                    className="mt-3"
                    style={{
                      width: "50px",
                      height: "3px",
                      background: "linear-gradient(45deg, #495057, #343a40)",
                      borderRadius: "2px",
                      margin: "0 auto",
                    }}
                  ></div>
                </div>
              </div>
            </div>

            <div className="col-lg-4 col-md-6 mb-4">
              <div
                className={`card h-100 border-0 shadow-lg hover-lift card-3d ${
                  isVisible ? "animate-fade-in-up stagger-4" : ""
                }`}
                style={{
                  background: "rgba(255, 255, 255, 0.95)",
                  backdropFilter: "blur(10px)",
                  borderRadius: "20px",
                  transition: "all 0.3s ease",
                }}
              >
                <div className="card-body p-5">
                  <div className="building-icon mb-4">
                    <i
                      className="fas fa-hard-hat"
                      style={{
                        fontSize: "4rem",
                        background: "linear-gradient(45deg, #343a40, #212529)",
                        WebkitBackgroundClip: "text",
                        WebkitTextFillColor: "transparent",
                        backgroundClip: "text",
                        filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.2))",
                      }}
                    ></i>
                  </div>
                  <h5
                    className="card-title fw-bold mb-3"
                    style={{ color: "#333" }}
                  >
                    الإشراف على التنفيذ
                  </h5>
                  <p className="card-text text-muted lh-lg">
                    متابعة دقيقة لضمان تطبيق التصميم بأعلى جودة ومعايير الأمان
                  </p>
                  <div
                    className="mt-3"
                    style={{
                      width: "50px",
                      height: "3px",
                      background: "linear-gradient(45deg, #343a40, #212529)",
                      borderRadius: "2px",
                      margin: "0 auto",
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}

export default Home;
