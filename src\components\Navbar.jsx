import { Link } from "react-router-dom";

function Navbar() {
  return (
    <nav className="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
      <div className="container">
        <Link
          className="navbar-brand fw-bold"
          to="/"
          style={{ color: "#2c3e50" }}
        >
          SQUARE.
        </Link>
        <button
          className="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span className="navbar-toggler-icon"></span>
        </button>

        <div className="collapse navbar-collapse" id="navbarNav">
          <ul className="navbar-nav ms-auto">
            <li className="nav-item">
              <Link className="nav-link text-dark fw-500" to="/">
                About
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link text-dark fw-500" to="/portfolio">
                Portfolio
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link text-dark fw-500" to="/services">
                Services
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link text-dark fw-500" to="/about">
                Testimony
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link text-dark fw-500" to="/blog">
                Blog
              </Link>
            </li>
            <li className="nav-item">
              <Link className="nav-link text-dark fw-500" to="/contact">
                Contact
              </Link>
            </li>
            <li className="nav-item ms-3">
              <Link
                className="btn btn-success px-4 py-2"
                to="/chat"
                style={{
                  borderRadius: "25px",
                  fontWeight: "500",
                  fontSize: "0.9rem",
                }}
              >
                Request a quote
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  );
}

export default Navbar;
