const express = require("express");
const Faq = require("../models/Faq");
const router = express.Router();

// للتوافق مع ChatBot.jsx
router.post("/faq/ask", async (req, res) => {
  try {
    const { question } = req.body;
    
    const faq = await Faq.findOne({
      question: { $regex: question, $options: "i" }
    });
    
    if (faq) {
      res.json({ answer: faq.answer });
    } else {
      res.json({ answer: "عذراً، لا أعرف إجابة هذا السؤال. يمكنك التواصل معي مباشرة." });
    }
  } catch (error) {
    res.status(500).json({ error: "خطأ في الخادم" });
  }
});

// للتوافق مع chat.jsx
router.post("/faq", async (req, res) => {
  try {
    const { question } = req.body;
    
    const faq = await Faq.findOne({
      question: { $regex: question, $options: "i" }
    });
    
    if (faq) {
      res.json({ answer: faq.answer });
    } else {
      res.json({ answer: "Sorry, I don't know that." });
    }
  } catch (error) {
    res.status(500).json({ error: "Server error. Try again later." });
  }
});

module.exports = router;


