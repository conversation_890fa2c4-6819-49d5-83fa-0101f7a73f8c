const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
require("dotenv").config();

const app = express();
const port = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static("public"));

// Database connection
mongoose.connect("mongodb://127.0.0.1:27017/architectbot", {
  useNewUrlParser: true,
  useUnifiedTopology: true,
}).then(() => {
  console.log("✅ متصل بقاعدة البيانات");
}).catch(err => {
  console.error("❌ خطأ في الاتصال بقاعدة البيانات:", err);
});

// Routes
const faqRoutes = require("./routes/faq");
app.use("/api", faqRoutes);

// Start server
app.listen(port, () => {
  console.log(`🚀 Server running on http://localhost:${port}`);
});
