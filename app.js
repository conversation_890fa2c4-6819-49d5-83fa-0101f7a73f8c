const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
require("dotenv").config();

const app = express();
const port = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static("public"));

// Log all requests
app.use((req, res, next) => {
  console.log(`${req.method} ${req.path}`, req.body);
  next();
});

// Database connection
mongoose
  .connect("mongodb://127.0.0.1:27017/architectbot", {})
  .then(() => {
    console.log("✅ connected to DB");
  })
  .catch((err) => {
    console.error("❌ Error connecting to DB:", err);
  });

// Routes
const faqRoutes = require("./routes/faq");
app.use("/api", faqRoutes);

// Start server
app.listen(port, () => {
  console.log(`🚀 Server running on http://localhost:${port}`);
});
