import { useState, useEffect } from "react";
import axios from "axios";

function Chat() {
  const [messages, setMessages] = useState([
    {
      sender: "bot",
      text: "مرحباً! أنا ArchiBot. اسألني أي سؤال أو اختر من الأسئلة الشائعة أدناه:",
    },
  ]);
  const [input, setInput] = useState("");
  const [showQuestions, setShowQuestions] = useState(true);

  // الأسئلة الشائعة
  const commonQuestions = [
    "ما هي الخدمات التي تقدمها؟",
    "كم تكلفة التصميم لمشروع صغير؟",
    "ما هي مواعيد العمل؟",
    "هل تقدم استشارات مجانية؟",
    "كم يستغرق تسليم التصميم؟",
    "هل تقدم رسومات ثلاثية الأبعاد؟",
  ];

  const sendMessage = async (questionText = null) => {
    const question = questionText || input;
    if (!question.trim()) return;

    const userMessage = { sender: "user", text: question };
    setMessages((prev) => [...prev, userMessage]);
    setShowQuestions(false); // إخفاء الأسئلة بعد إرسال أول سؤال

    try {
      const res = await axios.post("/api/faq", {
        question: question,
      });

      setMessages((prev) => [
        ...prev,
        {
          sender: "bot",
          text: res.data.answer || "عذراً، لا أعرف إجابة هذا السؤال.",
        },
      ]);
    } catch (err) {
      setMessages((prev) => [
        ...prev,
        { sender: "bot", text: "حدث خطأ في الخادم. حاول مرة أخرى." },
      ]);
    }

    setInput("");
  };

  const handleQuestionClick = (question) => {
    sendMessage(question);
  };

  return (
    <div className="container" style={{ maxWidth: "800px" }}>
      <h2 className="text-center my-4">💬 محادثة مع ArchiBot</h2>
      <div
        className="border rounded p-3 mb-3"
        style={{ height: "400px", overflowY: "auto" }}
      >
        {messages.map((msg, idx) => (
          <div
            key={idx}
            className={`text-${msg.sender === "user" ? "end" : "start"} mb-2`}
          >
            <span
              className={`d-inline-block p-2 rounded ${
                msg.sender === "user" ? "bg-primary text-white" : "bg-light"
              }`}
            >
              {msg.text}
            </span>
          </div>
        ))}

        {/* الأسئلة الشائعة */}
        {showQuestions && (
          <div className="mt-3 p-3 bg-light rounded">
            <h6 className="text-primary mb-3">
              <i className="bi bi-question-circle"></i> الأسئلة الشائعة:
            </h6>
            <div className="row">
              {commonQuestions.map((question, idx) => (
                <div key={idx} className="col-md-6 mb-2">
                  <button
                    className="btn btn-outline-primary btn-sm w-100 text-start"
                    onClick={() => handleQuestionClick(question)}
                    style={{
                      fontSize: "0.85rem",
                      whiteSpace: "normal",
                      textAlign: "right",
                    }}
                  >
                    {question}
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="d-flex mb-2">
        <input
          className="form-control me-2"
          placeholder="اكتب سؤالك هنا..."
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={(e) => e.key === "Enter" && sendMessage()}
        />
        <button className="btn btn-success me-2" onClick={() => sendMessage()}>
          إرسال
        </button>
        {!showQuestions && (
          <button
            className="btn btn-outline-secondary"
            onClick={() => setShowQuestions(true)}
            title="إظهار الأسئلة الشائعة"
          >
            ❓
          </button>
        )}
      </div>
    </div>
  );
}

export default Chat;
