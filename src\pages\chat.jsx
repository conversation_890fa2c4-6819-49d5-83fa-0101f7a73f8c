import { useState, useEffect } from "react";
import axios from "axios";

function Chat() {
  const [messages, setMessages] = useState([
    { sender: "bot", text: "Hi! I'm ArchiBot. Ask me anything!" },
  ]);
  const [input, setInput] = useState("");

  const sendMessage = async () => {
    if (!input.trim()) return;

    const userMessage = { sender: "user", text: input };
    setMessages((prev) => [...prev, userMessage]);

    try {
      const res = await axios.post("http://localhost:3000/api/faq", {
        question: input,
      });

      setMessages((prev) => [
        ...prev,
        { sender: "bot", text: res.data.answer || "Sorry, I don't know that." },
      ]);
    } catch (err) {
      setMessages((prev) => [
        ...prev,
        { sender: "bot", text: "Server error. Try again later." },
      ]);
    }

    setInput("");
  };

  return (
    <div className="container">
      <h2 className="text-center my-4">💬 Chat with ArchiBot</h2>
      <div className="border rounded p-3 mb-3" style={{ height: "400px", overflowY: "auto" }}>
        {messages.map((msg, idx) => (
          <div
            key={idx}
            className={`text-${msg.sender === "user" ? "end" : "start"} mb-2`}
          >
            <span
              className={`d-inline-block p-2 rounded ${
                msg.sender === "user" ? "bg-primary text-white" : "bg-light"
              }`}
            >
              {msg.text}
            </span>
          </div>
        ))}
      </div>

      <div className="d-flex">
        <input
          className="form-control me-2"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={(e) => e.key === "Enter" && sendMessage()}
        />
        <button className="btn btn-success" onClick={sendMessage}>
          Send
        </button>
      </div>
    </div>
  );
}

export default Chat;
