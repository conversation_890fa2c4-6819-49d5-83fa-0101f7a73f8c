const express = require("express");
const router = express.Router();
const Faq = require("../models/Faq");

router.post("/", async (req, res) => {
  const { question } = req.body;

  if (!question) return res.status(400).json({ message: "Question required" });

  const faq = await Faq.findOne({ question: { $regex: new RegExp(question, "i") } });

  if (!faq) return res.json({ answer: "Sorry, I don't have an answer for that." });

  res.json({ answer: faq.answer });
});

module.exports = router;
