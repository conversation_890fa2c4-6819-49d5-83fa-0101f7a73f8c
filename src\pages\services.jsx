function Services() {
  const services = [
    {
      title: "Architectural Design",
      description:
        "Creative and functional architectural design for residential, commercial, and public spaces.",
      icon: "🏛️",
    },
    {
      title: "Interior Design",
      description:
        "Elegant and modern interior design solutions tailored to your style and needs.",
      icon: "🛋️",
    },
    {
      title: "3D Visualization",
      description:
        "High-quality 3D renderings to help you visualize the final outcome before construction.",
      icon: "🎨",
    },
    {
      title: "Construction Supervision",
      description:
        "Professional site supervision to ensure the execution matches the design and standards.",
      icon: "🧱",
    },
    {
      title: "Urban Planning",
      description:
        "Strategic layout planning for sustainable urban and community development.",
      icon: "🌆",
    },
  ];

  return (
    <div className="container">
      <h2 className="mb-4 text-center">🛠 Services</h2>
      <div className="row">
        {services.map((service, index) => (
          <div className="col-md-4 mb-4" key={index}>
            <div className="card h-100 text-center shadow">
              <div className="card-body">
                <div style={{ fontSize: "2rem" }}>{service.icon}</div>
                <h5 className="card-title mt-3">{service.title}</h5>
                <p className="card-text">{service.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default Services;
