import "../styles/animations.css";

function Services() {
  const services = [
    {
      title: "التصميم المعماري",
      description:
        "تصميم معماري إبداعي ووظيفي للمساحات السكنية والتجارية والعامة مع مراعاة أحدث المعايير العالمية.",
      icon: "🏛️",
      color: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
    },
    {
      title: "التصميم الداخلي",
      description:
        "حلول تصميم داخلي أنيقة وعصرية مصممة خصيصاً لتناسب ذوقك واحتياجاتك الشخصية.",
      icon: "🛋️",
      color: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
    },
    {
      title: "التصور ثلاثي الأبعاد",
      description:
        "عروض ثلاثية الأبعاد عالية الجودة لمساعدتك على تصور النتيجة النهائية قبل البدء في البناء.",
      icon: "🎨",
      color: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
    },
    {
      title: "إشراف البناء",
      description:
        "إشراف مهني على الموقع لضمان تنفيذ المشروع وفقاً للتصميم والمعايير المطلوبة.",
      icon: "🧱",
      color: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
    },
    {
      title: "التخطيط العمراني",
      description:
        "تخطيط استراتيجي للمخططات العمرانية المستدامة وتطوير المجتمعات الحديثة.",
      icon: "🌆",
      color: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
    },
    {
      title: "الاستشارات الهندسية",
      description:
        "استشارات هندسية متخصصة في جميع مراحل المشروع من التخطيط إلى التنفيذ.",
      icon: "📐",
      color: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
    },
  ];

  return (
    <div
      className="min-vh-100 d-flex flex-column position-relative"
      style={{
        background:
          "linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab)",
        backgroundSize: "400% 400%",
        animation: "gradientShift 15s ease infinite",
      }}
    >
      {/* Animated Background Elements */}
      <div className="position-absolute w-100 h-100 overflow-hidden">
        {/* Floating geometric shapes */}
        <div
          className="position-absolute animate-float-slow"
          style={{ top: "10%", left: "10%" }}
        >
          <div
            style={{
              width: "60px",
              height: "60px",
              background: "rgba(255,255,255,0.1)",
              transform: "rotate(45deg)",
              borderRadius: "12px",
            }}
          ></div>
        </div>

        <div
          className="position-absolute animate-float-medium"
          style={{ top: "20%", right: "15%" }}
        >
          <div
            style={{
              width: "40px",
              height: "40px",
              background: "rgba(255,255,255,0.08)",
              borderRadius: "50%",
            }}
          ></div>
        </div>

        <div
          className="position-absolute animate-float-fast"
          style={{ bottom: "20%", left: "20%" }}
        >
          <div
            style={{
              width: "50px",
              height: "50px",
              background: "rgba(255,255,255,0.06)",
              transform: "rotate(30deg)",
              borderRadius: "8px",
            }}
          ></div>
        </div>
      </div>

      {/* Header Section */}
      <section className="py-5 text-white position-relative">
        <div className="container text-center">
          <h1
            className="display-4 fw-bold mb-4 animate-fade-in-up text-white"
            style={{
              textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
            }}
          >
            🛠️ خدماتنا المتميزة
          </h1>
          <p
            className="lead mb-5 animate-fade-in-up text-shadow"
            style={{
              animationDelay: "0.2s",
              textShadow: "1px 1px 2px rgba(0,0,0,0.3)",
              fontSize: "1.2rem",
            }}
          >
            نقدم مجموعة شاملة من الخدمات المعمارية والهندسية المتطورة
          </p>
        </div>
      </section>

      {/* Services Grid */}
      <section className="flex-grow-1 py-5 position-relative">
        <div className="container">
          <div className="row g-4">
            {services.map((service, index) => (
              <div
                className="col-lg-4 col-md-6 mb-4 animate-fade-in-up"
                key={index}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div
                  className="card h-100 text-center shadow-lg hover-lift border-0 position-relative overflow-hidden"
                  style={{
                    background: "rgba(255, 255, 255, 0.1)",
                    backdropFilter: "blur(15px)",
                    border: "2px solid rgba(255, 255, 255, 0.2)",
                    borderRadius: "20px",
                    transition: "all 0.3s ease",
                  }}
                >
                  {/* Card Background Gradient */}
                  <div
                    className="position-absolute w-100 h-100"
                    style={{
                      background: service.color,
                      opacity: 0.1,
                      borderRadius: "18px",
                    }}
                  ></div>

                  <div className="card-body p-4 position-relative">
                    <div
                      className="mb-4 animate-bounce-slow"
                      style={{
                        fontSize: "3rem",
                        filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.2))",
                      }}
                    >
                      {service.icon}
                    </div>
                    <h5
                      className="card-title mb-3 fw-bold text-white text-shadow"
                      style={{
                        fontSize: "1.4rem",
                        textShadow: "1px 1px 2px rgba(0,0,0,0.3)",
                      }}
                    >
                      {service.title}
                    </h5>
                    <p
                      className="card-text text-white-50 lh-lg"
                      style={{
                        fontSize: "1rem",
                        textShadow: "1px 1px 2px rgba(0,0,0,0.2)",
                      }}
                    >
                      {service.description}
                    </p>

                    {/* Decorative border */}
                    <div
                      className="position-absolute bottom-0 start-50 translate-middle-x"
                      style={{
                        width: "60px",
                        height: "4px",
                        background: service.color,
                        borderRadius: "2px",
                        marginBottom: "10px",
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}

export default Services;
