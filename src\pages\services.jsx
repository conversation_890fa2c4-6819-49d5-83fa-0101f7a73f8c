import "../styles/animations.css";

function Services() {
  const services = [
    {
      title: "التصميم المعماري",
      description:
        "تصميم معماري إبداعي ووظيفي للمساحات السكنية والتجارية والعامة مع مراعاة أحدث المعايير العالمية.",
      icon: "🏛️",
      color: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
    },
    {
      title: "التصميم الداخلي",
      description:
        "حلول تصميم داخلي أنيقة وعصرية مصممة خصيصاً لتناسب ذوقك واحتياجاتك الشخصية.",
      icon: "🛋️",
      color: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
    },
    {
      title: "التصور ثلاثي الأبعاد",
      description:
        "عروض ثلاثية الأبعاد عالية الجودة لمساعدتك على تصور النتيجة النهائية قبل البدء في البناء.",
      icon: "🎨",
      color: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
    },
    {
      title: "إشراف البناء",
      description:
        "إشراف مهني على الموقع لضمان تنفيذ المشروع وفقاً للتصميم والمعايير المطلوبة.",
      icon: "🧱",
      color: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
    },
    {
      title: "التخطيط العمراني",
      description:
        "تخطيط استراتيجي للمخططات العمرانية المستدامة وتطوير المجتمعات الحديثة.",
      icon: "🌆",
      color: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
    },
    {
      title: "الاستشارات الهندسية",
      description:
        "استشارات هندسية متخصصة في جميع مراحل المشروع من التخطيط إلى التنفيذ.",
      icon: "📐",
      color: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
    },
  ];

  return (
    <div
      className="min-vh-100 d-flex flex-column position-relative overflow-hidden"
      style={{
        background:
          "linear-gradient(-45deg, #6c757d, #495057, #343a40, #212529)",
        backgroundSize: "400% 400%",
        animation: "grayGradientShift 15s ease infinite",
      }}
    >
      {/* Animated Background Elements */}
      <div className="position-absolute w-100 h-100 overflow-hidden">
        {/* Floating Architecture Icons */}
        <div
          className="position-absolute animate-float-slow"
          style={{ top: "15%", left: "8%" }}
        >
          <i
            className="fas fa-building"
            style={{
              fontSize: "4rem",
              color: "rgba(255,255,255,0.15)",
              filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.2))",
            }}
          ></i>
        </div>

        <div
          className="position-absolute animate-float-medium"
          style={{ top: "25%", right: "12%" }}
        >
          <i
            className="fas fa-home"
            style={{
              fontSize: "3rem",
              color: "rgba(255,255,255,0.12)",
              filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.2))",
            }}
          ></i>
        </div>

        <div
          className="position-absolute animate-float-fast"
          style={{ top: "45%", left: "15%" }}
        >
          <i
            className="fas fa-city"
            style={{
              fontSize: "3.5rem",
              color: "rgba(255,255,255,0.1)",
              filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.2))",
            }}
          ></i>
        </div>

        <div
          className="position-absolute animate-float-slow"
          style={{ bottom: "30%", right: "8%" }}
        >
          <i
            className="fas fa-drafting-compass"
            style={{
              fontSize: "2.5rem",
              color: "rgba(255,255,255,0.13)",
              filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.2))",
            }}
          ></i>
        </div>

        <div
          className="position-absolute animate-float-medium"
          style={{ bottom: "15%", left: "25%" }}
        >
          <i
            className="fas fa-ruler-combined"
            style={{
              fontSize: "3rem",
              color: "rgba(255,255,255,0.11)",
              filter: "drop-shadow(0 4px 8px rgba(0,0,0,0.2))",
            }}
          ></i>
        </div>

        {/* Geometric Shapes */}
        <div
          className="position-absolute animate-rotate-slow"
          style={{ top: "10%", right: "20%" }}
        >
          <div
            style={{
              width: "80px",
              height: "80px",
              background:
                "linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))",
              transform: "rotate(45deg)",
              borderRadius: "15px",
              border: "2px solid rgba(255,255,255,0.2)",
            }}
          ></div>
        </div>

        <div
          className="position-absolute animate-float-fast"
          style={{ bottom: "40%", right: "25%" }}
        >
          <div
            style={{
              width: "60px",
              height: "60px",
              background:
                "linear-gradient(135deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03))",
              borderRadius: "50%",
              border: "1px solid rgba(255,255,255,0.15)",
            }}
          ></div>
        </div>

        {/* Floating Particles */}
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="position-absolute animate-particle"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 15}s`,
              animationDuration: `${15 + Math.random() * 10}s`,
            }}
          >
            <div
              style={{
                width: "4px",
                height: "4px",
                background: "rgba(255,255,255,0.6)",
                borderRadius: "50%",
                boxShadow: "0 0 10px rgba(255,255,255,0.8)",
              }}
            ></div>
          </div>
        ))}
      </div>

      {/* Header Section */}
      <section className="py-5 text-white position-relative">
        <div className="container text-center">
          <h1
            className="display-4 fw-bold mb-4 animate-fade-in-up text-white"
            style={{
              textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
            }}
          >
            🛠️ خدماتنا المتميزة
          </h1>
          <p
            className="lead mb-5 animate-fade-in-up text-shadow"
            style={{
              animationDelay: "0.2s",
              textShadow: "1px 1px 2px rgba(0,0,0,0.3)",
              fontSize: "1.2rem",
            }}
          >
            نقدم مجموعة شاملة من الخدمات المعمارية والهندسية المتطورة
          </p>
        </div>
      </section>

      {/* Services Grid */}
      <section className="flex-grow-1 py-5 position-relative">
        <div className="container">
          <div className="row g-4">
            {services.map((service, index) => (
              <div
                className="col-lg-4 col-md-6 mb-4 animate-fade-in-up"
                key={index}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div
                  className="card h-100 text-center shadow-lg hover-lift border-0 position-relative overflow-hidden card-3d glow-effect"
                  style={{
                    background: "rgba(255, 255, 255, 0.15)",
                    backdropFilter: "blur(20px)",
                    border: "2px solid rgba(255, 255, 255, 0.3)",
                    borderRadius: "25px",
                    transition: "all 0.4s ease",
                    cursor: "pointer",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform =
                      "translateY(-10px) scale(1.02)";
                    e.currentTarget.style.boxShadow =
                      "0 20px 40px rgba(0,0,0,0.3)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = "translateY(0) scale(1)";
                    e.currentTarget.style.boxShadow =
                      "0 10px 25px rgba(0,0,0,0.15)";
                  }}
                >
                  {/* Animated Background Gradient */}
                  <div
                    className="position-absolute w-100 h-100 animate-shimmer"
                    style={{
                      background: service.color,
                      opacity: 0.15,
                      borderRadius: "23px",
                    }}
                  ></div>

                  {/* Glowing Border Effect */}
                  <div
                    className="position-absolute w-100 h-100"
                    style={{
                      background: `linear-gradient(45deg, transparent, ${
                        service.color.split(",")[0].split("(")[1]
                      }, transparent)`,
                      opacity: 0.3,
                      borderRadius: "23px",
                      filter: "blur(1px)",
                    }}
                  ></div>

                  <div className="card-body p-5 position-relative">
                    {/* Icon with Animation */}
                    <div
                      className="mb-4 position-relative"
                      style={{
                        fontSize: "4rem",
                        filter: "drop-shadow(0 8px 16px rgba(0,0,0,0.3))",
                      }}
                    >
                      <div className="animate-bounce-slow position-relative">
                        {service.icon}
                        {/* Icon Glow Effect */}
                        <div
                          className="position-absolute top-0 start-0 w-100 h-100"
                          style={{
                            background: service.color,
                            opacity: 0.2,
                            borderRadius: "50%",
                            filter: "blur(20px)",
                            zIndex: -1,
                          }}
                        ></div>
                      </div>
                    </div>

                    {/* Title with Gradient */}
                    <h5
                      className="card-title mb-4 fw-bold position-relative"
                      style={{
                        fontSize: "1.5rem",
                        background: "linear-gradient(45deg, #fff, #f8f9fa)",
                        WebkitBackgroundClip: "text",
                        WebkitTextFillColor: "transparent",
                        backgroundClip: "text",
                        textShadow: "2px 2px 4px rgba(0,0,0,0.3)",
                      }}
                    >
                      {service.title}
                      {/* Title Underline */}
                      <div
                        className="position-absolute bottom-0 start-50 translate-middle-x"
                        style={{
                          width: "50px",
                          height: "2px",
                          background: service.color,
                          borderRadius: "1px",
                          marginTop: "8px",
                        }}
                      ></div>
                    </h5>

                    {/* Description */}
                    <p
                      className="card-text text-white lh-lg mb-4"
                      style={{
                        fontSize: "1.1rem",
                        textShadow: "1px 1px 2px rgba(0,0,0,0.4)",
                        opacity: 0.9,
                      }}
                    >
                      {service.description}
                    </p>

                    {/* Action Button */}
                    <button
                      className="btn btn-outline-light hover-lift px-4 py-2"
                      style={{
                        borderRadius: "20px",
                        border: "2px solid rgba(255,255,255,0.4)",
                        background: "rgba(255,255,255,0.1)",
                        backdropFilter: "blur(10px)",
                        transition: "all 0.3s ease",
                      }}
                      onMouseEnter={(e) => {
                        e.target.style.background = service.color;
                        e.target.style.borderColor = "transparent";
                        e.target.style.transform = "scale(1.05)";
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.background = "rgba(255,255,255,0.1)";
                        e.target.style.borderColor = "rgba(255,255,255,0.4)";
                        e.target.style.transform = "scale(1)";
                      }}
                    >
                      <i className="fas fa-arrow-left me-2"></i>
                      اعرف المزيد
                    </button>

                    {/* Decorative Corner Elements */}
                    <div
                      className="position-absolute"
                      style={{
                        top: "15px",
                        right: "15px",
                        width: "30px",
                        height: "30px",
                        background: service.color,
                        opacity: 0.3,
                        borderRadius: "50%",
                        filter: "blur(10px)",
                      }}
                    ></div>

                    <div
                      className="position-absolute"
                      style={{
                        bottom: "15px",
                        left: "15px",
                        width: "20px",
                        height: "20px",
                        background: service.color,
                        opacity: 0.2,
                        borderRadius: "50%",
                        filter: "blur(8px)",
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}

export default Services;
