import { useState, useEffect } from "react";
import "../styles/animations.css";

function Portfolio() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeCategory, setActiveCategory] = useState("all");

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const projects = [
    {
      id: 1,
      title: "فيلا سكنية عصرية",
      category: "residential",
      image: "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=400",
      description: "تصميم فيلا عصرية بمساحة 400 متر مربع"
    },
    {
      id: 2,
      title: "مجمع تجاري",
      category: "commercial",
      image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400",
      description: "مجمع تجاري متعدد الطوابق في وسط المدينة"
    },
    {
      id: 3,
      title: "مسجد حديث",
      category: "religious",
      image: "https://images.unsplash.com/photo-1564769662533-4f00a87b4056?w=400",
      description: "تصميم مسجد بطراز معماري حديث"
    },
    {
      id: 4,
      title: "شقة سكنية",
      category: "residential",
      image: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400",
      description: "تصميم داخلي لشقة سكنية 150 متر"
    },
    {
      id: 5,
      title: "مكتب إداري",
      category: "commercial",
      image: "https://images.unsplash.com/photo-1497366216548-37526070297c?w=400",
      description: "مكتب إداري بتصميم عصري ومريح"
    },
    {
      id: 6,
      title: "منزل ريفي",
      category: "residential",
      image: "https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=400",
      description: "منزل ريفي بطراز تقليدي محدث"
    }
  ];

  const categories = [
    { id: "all", name: "جميع المشاريع", icon: "fas fa-th" },
    { id: "residential", name: "سكني", icon: "fas fa-home" },
    { id: "commercial", name: "تجاري", icon: "fas fa-building" },
    { id: "religious", name: "ديني", icon: "fas fa-mosque" }
  ];

  const filteredProjects = activeCategory === "all" 
    ? projects 
    : projects.filter(project => project.category === activeCategory);

  return (
    <div className="portfolio-page">
      {/* Header */}
      <section className="py-5 bg-primary text-white">
        <div className="container text-center">
          <div className={`${isVisible ? 'animate-fade-in-up' : ''}`}>
            <h1 className="display-4 fw-bold mb-3">
              <i className="fas fa-portfolio me-3"></i>
              معرض الأعمال
            </h1>
            <p className="lead">
              مجموعة مختارة من أفضل مشاريعي المعمارية والتصميمية
            </p>
          </div>
        </div>
      </section>

      {/* Filter Categories */}
      <section className="py-4 bg-light">
        <div className="container">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              <div className={`d-flex flex-wrap justify-content-center gap-2 ${isVisible ? 'animate-scale-in stagger-2' : ''}`}>
                {categories.map((category, index) => (
                  <button
                    key={category.id}
                    className={`btn ${activeCategory === category.id ? 'btn-primary' : 'btn-outline-primary'} hover-scale`}
                    onClick={() => setActiveCategory(category.id)}
                  >
                    <i className={`${category.icon} me-2`}></i>
                    {category.name}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-5">
        <div className="container">
          <div className="row">
            {filteredProjects.map((project, index) => (
              <div key={project.id} className="col-lg-4 col-md-6 mb-4">
                <div className={`card h-100 border-0 shadow-sm hover-lift ${isVisible ? `animate-fade-in-up stagger-${(index % 6) + 1}` : ''}`}>
                  <div className="position-relative overflow-hidden">
                    <img 
                      src={project.image} 
                      className="card-img-top" 
                      alt={project.title}
                      style={{ height: "250px", objectFit: "cover" }}
                    />
                    <div className="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-0 hover-overlay d-flex align-items-center justify-content-center">
                      <button className="btn btn-light btn-lg opacity-0 hover-button">
                        <i className="fas fa-eye me-2"></i>
                        عرض التفاصيل
                      </button>
                    </div>
                  </div>
                  <div className="card-body p-4">
                    <h5 className="card-title text-primary">{project.title}</h5>
                    <p className="card-text text-muted">{project.description}</p>
                    <div className="d-flex justify-content-between align-items-center">
                      <small className="text-muted">
                        <i className="fas fa-calendar me-1"></i>
                        2024
                      </small>
                      <span className={`badge ${
                        project.category === 'residential' ? 'bg-success' :
                        project.category === 'commercial' ? 'bg-warning' :
                        'bg-info'
                      }`}>
                        {categories.find(cat => cat.id === project.category)?.name}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-5 bg-primary text-white">
        <div className="container text-center">
          <div className={`${isVisible ? 'animate-fade-in-up stagger-3' : ''}`}>
            <h3 className="mb-4">هل لديك مشروع في ذهنك؟</h3>
            <p className="lead mb-4">دعني أساعدك في تحويل أفكارك إلى واقع معماري مذهل</p>
            <button className="btn btn-light btn-lg px-5 py-3 hover-lift">
              <i className="fas fa-phone me-2"></i>
              تواصل معي الآن
            </button>
          </div>
        </div>
      </section>

      <style jsx>{`
        .hover-overlay {
          transition: background-color 0.3s ease;
        }
        
        .card:hover .hover-overlay {
          background-color: rgba(0,0,0,0.7) !important;
        }
        
        .hover-button {
          transition: opacity 0.3s ease, transform 0.3s ease;
          transform: translateY(20px);
        }
        
        .card:hover .hover-button {
          opacity: 1 !important;
          transform: translateY(0);
        }
      `}</style>
    </div>
  );
}

export default Portfolio;
